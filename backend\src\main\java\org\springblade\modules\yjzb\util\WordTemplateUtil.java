package org.springblade.modules.yjzb.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Word模板工具类
 */
@Slf4j
public class WordTemplateUtil {
    private static class RunInfo {
        String text;
        boolean bold;
        boolean italic;
        String color;
        int fontSize;
        String fontFamily;
        UnderlinePatterns underline;
        boolean strike;
        VerticalAlign subscript;
    }

    /**
     * 加载Word模板
     *
     * @param templatePath 模板路径（相对于classpath）
     * @return Word文档对象
     * @throws IOException 如果模板加载失败
     */
    public static XWPFDocument loadTemplate(String templatePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(templatePath);
        InputStream inputStream = resource.getInputStream();
        XWPFDocument document = new XWPFDocument(inputStream);
        inputStream.close();
        return document;
    }

    /**
     * 替换文档中的占位符
     *
     * @param document     Word文档对象
     * @param placeholders 占位符映射
     */
    public static void replacePlaceholders(XWPFDocument document, Map<String, String> placeholders) {
        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, placeholders);
        }

        // 替换表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceInParagraph(paragraph, placeholders);
                    }
                }
            }
        }
    }

    /**
     * 替换段落中的占位符并保留原有样式
     *
     * @param paragraph    段落对象
     * @param placeholders 占位符映射
     */
    /**
     * 替换段落中的占位符并保留每个 Run 的原始样式
     *
     * @param paragraph    段落对象
     * @param placeholders 占位符映射
     */
    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> placeholders) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        // 存储每个 Run 的原始样式信息和原始文本
        List<RunInfo> runInfos = new ArrayList<>();

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null) continue;

            RunInfo info = new RunInfo();
            info.text = text;
            info.bold = run.isBold();
            info.italic = run.isItalic();
            info.color = run.getColor();
            info.fontSize = run.getFontSize();
            info.fontFamily = run.getFontFamily();
            info.underline = run.getUnderline();
            info.strike = run.isStrike();
            info.subscript = run.getSubscript();

            runInfos.add(info);
        }

        // 合并段落文本用于替换占位符
        StringBuilder fullText = new StringBuilder();
        for (RunInfo info : runInfos) {
            fullText.append(info.text);
        }

        // 如果段落内没有任意占位符，直接返回，避免破坏原有格式（空格/制表位/Leader等）
        String full = fullText.toString();
        boolean hasPlaceholder = false;
        for (String key : placeholders.keySet()) {
            if (full.contains("${" + key + "}")) { hasPlaceholder = true; break; }
        }
        if (!hasPlaceholder) {
            return;
        }

        // 替换所有占位符
        String replacedText = full;
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue();
            if (value == null) value = "";
            replacedText = replacedText.replace(placeholder, value);
        }

        // 清除原有 Runs
        while (!paragraph.getRuns().isEmpty()) {
            paragraph.removeRun(0);
        }

        // 将替换后的内容重新按原始 Run 分割插入
        int remainingLength = replacedText.length();
        int offset = 0;

        for (RunInfo info : runInfos) {
            int lengthToUse = Math.min(info.text.length(), remainingLength);
            if (lengthToUse <= 0) continue;

            String newText = replacedText.substring(offset, offset + lengthToUse);

            XWPFRun newRun = paragraph.createRun();
            newRun.setBold(info.bold);
            newRun.setItalic(info.italic);
            if (info.color != null) newRun.setColor(info.color);
            if (info.fontSize > 0) newRun.setFontSize(info.fontSize);
            if (info.fontFamily != null) newRun.setFontFamily(info.fontFamily);
            if (info.underline != null && info.underline != UnderlinePatterns.NONE) {
                newRun.setUnderline(info.underline);
            } else {
                newRun.setUnderline(UnderlinePatterns.NONE);
            }
            newRun.setStrike(info.strike);
            newRun.setSubscript(info.subscript);

            // 处理换行符
            setTextWithLineBreaks(newRun, newText);

            offset += lengthToUse;
            remainingLength -= lengthToUse;
        }

        // 如果还有剩余文本，创建一个新 Run 插入（可选）
        if (remainingLength > 0) {
            String remainingText = replacedText.substring(offset);

            XWPFRun fallbackRun = paragraph.createRun();
            fallbackRun.setText(remainingText);

            // 可选：使用第一个 Run 的样式作为默认样式
            if (!runInfos.isEmpty()) {
                RunInfo defaultInfo = runInfos.get(0);
                fallbackRun.setBold(defaultInfo.bold);
                fallbackRun.setItalic(defaultInfo.italic);
                if (defaultInfo.color != null) fallbackRun.setColor(defaultInfo.color);
                if (defaultInfo.fontSize > 0) fallbackRun.setFontSize(defaultInfo.fontSize);
                if (defaultInfo.fontFamily != null) fallbackRun.setFontFamily(defaultInfo.fontFamily);
                fallbackRun.setUnderline(defaultInfo.underline);
                fallbackRun.setStrike(defaultInfo.strike);
                fallbackRun.setSubscript(defaultInfo.subscript);
            }
        }
    }


    /**
     * 设置文本并处理换行符
     *
     * @param run  XWPFRun对象
     * @param text 要设置的文本
     */
    private static void setTextWithLineBreaks(XWPFRun run, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        String[] lines = text.split("\\r?\\n");
        for (int i = 0; i < lines.length; i++) {
            if (i > 0) {
                run.addBreak(); // 添加换行符
            }
            run.setText(lines[i]);
        }
    }

    /**
     * 替换表格内容，支持JSON格式数据
     *
     * @param document        Word文档对象
     * @param searchText      要搜索的文本
     * @param jsonReplacement JSON格式的表格数据
     */
    public static void replaceTableContent(XWPFDocument document, String searchText, String jsonReplacement) {
        try {
            // 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> tableData = objectMapper.readValue(jsonReplacement,
                new TypeReference<List<List<String>>>() {});

            // 查找包含搜索文本的表格
            for (XWPFTable table : document.getTables()) {
                boolean found = false;
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            String cellText = paragraph.getText();
                            if (cellText != null && cellText.contains(searchText)) {
                                found = true;
                                break;
                            }
                        }
                        if (found) break;
                    }
                    if (found) break;
                }

                if (found) {
                    // 替换表格数据
                    replaceTableWithData(table, tableData);
                    return;
                }
            }

            // 如果没有找到现有表格，创建新表格
            createNewTableWithData(document, tableData);

        } catch (Exception e) {
            log.error("解析表格JSON数据失败: {}", jsonReplacement, e);
            // 如果JSON解析失败，按普通文本处理
            Map<String, String> placeholder = new java.util.HashMap<>();
            placeholder.put(searchText.replace("${", "").replace("}", ""), jsonReplacement);
            replacePlaceholders(document, placeholder);
        }
    }

    /**
     * 用新数据替换现有表格
     */
    private static void replaceTableWithData(XWPFTable table, List<List<String>> tableData) {
        // 清空现有表格内容
        while (table.getRows().size() > 0) {
            table.removeRow(0);
        }

        // 填充新数据
        for (int i = 0; i < tableData.size(); i++) {
            List<String> rowData = tableData.get(i);
            XWPFTableRow row = table.createRow();

            // 确保行有足够的单元格
            while (row.getTableCells().size() < rowData.size()) {
                row.addNewTableCell();
            }

            // 填充单元格数据
            for (int j = 0; j < rowData.size() && j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getTableCells().get(j);
                cell.setText(rowData.get(j));

                // 设置单元格样式
                if (i == 0) { // 表头
                    XWPFParagraph paragraph = cell.getParagraphs().get(0);
                    XWPFRun run = paragraph.getRuns().get(0);
                    if (run != null) {
                        run.setBold(true);
                        run.setFontSize(11);
                    }
                }
            }
        }
    }

    /**
     * 创建新表格
     */
    private static void createNewTableWithData(XWPFDocument document, List<List<String>> tableData) {
        if (tableData.isEmpty()) {
            return;
        }

        XWPFTable table = document.createTable();

        for (int i = 0; i < tableData.size(); i++) {
            List<String> rowData = tableData.get(i);
            XWPFTableRow row = (i == 0) ? table.getRow(0) : table.createRow();

            // 确保行有足够的单元格
            while (row.getTableCells().size() < rowData.size()) {
                row.addNewTableCell();
            }

            // 填充单元格数据
            for (int j = 0; j < rowData.size() && j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getTableCells().get(j);
                cell.setText(rowData.get(j));

                // 设置单元格样式
                if (i == 0) { // 表头
                    XWPFParagraph paragraph = cell.getParagraphs().get(0);
                    XWPFRun run = paragraph.createRun();
                    run.setText(rowData.get(j));
                    run.setBold(true);
                    run.setFontSize(11);
                }
            }
        }
    }

    /**
     * 将文档写入输出流
     *
     * @param document       Word文档对象
     * @param outputStream 输出流
     * @throws IOException 如果写入失败
     */
    public static void writeDocument(XWPFDocument document, OutputStream outputStream) throws IOException {
        document.write(outputStream);
    }
}
