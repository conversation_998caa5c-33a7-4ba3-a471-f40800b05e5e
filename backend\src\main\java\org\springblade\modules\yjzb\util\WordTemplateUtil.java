package org.springblade.modules.yjzb.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Word模板工具类
 */
@Slf4j
public class WordTemplateUtil {
    private static class RunInfo {
        String text;
        boolean bold;
        boolean italic;
        String color;
        int fontSize;
        String fontFamily;
        UnderlinePatterns underline;
        boolean strike;
        VerticalAlign subscript;
    }

    /**
     * 加载Word模板
     *
     * @param templatePath 模板路径（相对于classpath）
     * @return Word文档对象
     * @throws IOException 如果模板加载失败
     */
    public static XWPFDocument loadTemplate(String templatePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(templatePath);
        InputStream inputStream = resource.getInputStream();
        XWPFDocument document = new XWPFDocument(inputStream);
        inputStream.close();
        return document;
    }

    /**
     * 替换文档中的占位符
     *
     * @param document     Word文档对象
     * @param placeholders 占位符映射
     */
    public static void replacePlaceholders(XWPFDocument document, Map<String, String> placeholders) {
        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, placeholders);
        }

        // 替换表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceInParagraph(paragraph, placeholders);
                    }
                }
            }
        }
    }

    /**
     * 替换段落中的占位符并保留原有样式
     *
     * @param paragraph    段落对象
     * @param placeholders 占位符映射
     */
    /**
     * 替换段落中的占位符并保留每个 Run 的原始样式
     *
     * @param paragraph    段落对象
     * @param placeholders 占位符映射
     */
    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> placeholders) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        // 存储每个 Run 的原始样式信息和原始文本
        List<RunInfo> runInfos = new ArrayList<>();

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null) continue;

            RunInfo info = new RunInfo();
            info.text = text;
            info.bold = run.isBold();
            info.italic = run.isItalic();
            info.color = run.getColor();
            info.fontSize = run.getFontSize();
            info.fontFamily = run.getFontFamily();
            info.underline = run.getUnderline();
            info.strike = run.isStrike();
            info.subscript = run.getSubscript();

            runInfos.add(info);
        }

        // 合并段落文本用于替换占位符
        StringBuilder fullText = new StringBuilder();
        for (RunInfo info : runInfos) {
            fullText.append(info.text);
        }

        // 如果段落内没有任意占位符，直接返回，避免破坏原有格式（空格/制表位/Leader等）
        String full = fullText.toString();
        boolean hasPlaceholder = false;
        for (String key : placeholders.keySet()) {
            if (full.contains("${" + key + "}")) { hasPlaceholder = true; break; }
        }
        if (!hasPlaceholder) {
            return;
        }

        // 替换所有占位符
        String replacedText = full;
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue();
            if (value == null) value = "";
            replacedText = replacedText.replace(placeholder, value);
        }

        // 清除原有 Runs
        while (!paragraph.getRuns().isEmpty()) {
            paragraph.removeRun(0);
        }

        // 检查替换后的文本是否包含换行符
        if (replacedText.contains("\n") || replacedText.contains("\r")) {
            // 如果包含换行符，创建一个新的Run来处理整个文本
            XWPFRun newRun = paragraph.createRun();

            // 使用第一个Run的样式作为默认样式
            if (!runInfos.isEmpty()) {
                RunInfo defaultInfo = runInfos.get(0);
                newRun.setBold(defaultInfo.bold);
                newRun.setItalic(defaultInfo.italic);
                if (defaultInfo.color != null) newRun.setColor(defaultInfo.color);
                if (defaultInfo.fontSize > 0) newRun.setFontSize(defaultInfo.fontSize);
                if (defaultInfo.fontFamily != null) newRun.setFontFamily(defaultInfo.fontFamily);
                if (defaultInfo.underline != null && defaultInfo.underline != UnderlinePatterns.NONE) {
                    newRun.setUnderline(defaultInfo.underline);
                } else {
                    newRun.setUnderline(UnderlinePatterns.NONE);
                }
                newRun.setStrike(defaultInfo.strike);
                newRun.setSubscript(defaultInfo.subscript);
            }

            // 处理包含换行符的文本
            setTextWithLineBreaks(newRun, replacedText);
        } else {
            // 如果不包含换行符，按原来的方式分割插入
            int remainingLength = replacedText.length();
            int offset = 0;

            for (RunInfo info : runInfos) {
                int lengthToUse = Math.min(info.text.length(), remainingLength);
                if (lengthToUse <= 0) continue;

                String newText = replacedText.substring(offset, offset + lengthToUse);

                XWPFRun newRun = paragraph.createRun();
                newRun.setBold(info.bold);
                newRun.setItalic(info.italic);
                if (info.color != null) newRun.setColor(info.color);
                if (info.fontSize > 0) newRun.setFontSize(info.fontSize);
                if (info.fontFamily != null) newRun.setFontFamily(info.fontFamily);
                if (info.underline != null && info.underline != UnderlinePatterns.NONE) {
                    newRun.setUnderline(info.underline);
                } else {
                    newRun.setUnderline(UnderlinePatterns.NONE);
                }
                newRun.setStrike(info.strike);
                newRun.setSubscript(info.subscript);

                newRun.setText(newText);

                offset += lengthToUse;
                remainingLength -= lengthToUse;
            }

            // 如果还有剩余文本，创建一个新 Run 插入
            if (remainingLength > 0) {
                String remainingText = replacedText.substring(offset);

                XWPFRun fallbackRun = paragraph.createRun();
                fallbackRun.setText(remainingText);

                // 使用第一个 Run 的样式作为默认样式
                if (!runInfos.isEmpty()) {
                    RunInfo defaultInfo = runInfos.get(0);
                    fallbackRun.setBold(defaultInfo.bold);
                    fallbackRun.setItalic(defaultInfo.italic);
                    if (defaultInfo.color != null) fallbackRun.setColor(defaultInfo.color);
                    if (defaultInfo.fontSize > 0) fallbackRun.setFontSize(defaultInfo.fontSize);
                    if (defaultInfo.fontFamily != null) fallbackRun.setFontFamily(defaultInfo.fontFamily);
                    fallbackRun.setUnderline(defaultInfo.underline);
                    fallbackRun.setStrike(defaultInfo.strike);
                    fallbackRun.setSubscript(defaultInfo.subscript);
                }
            }
        }
    }


    /**
     * 设置文本并处理换行符
     *
     * @param run  XWPFRun对象
     * @param text 要设置的文本
     */
    private static void setTextWithLineBreaks(XWPFRun run, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        // 处理不同类型的换行符
        String[] lines = text.split("\\r?\\n");

        // 设置第一行文本
        if (lines.length > 0) {
            run.setText(lines[0], 0); // 使用setText(text, 0)设置第一行
        }

        // 处理后续行，每行前添加换行符
        for (int i = 1; i < lines.length; i++) {
            run.addBreak(); // 添加换行符
            run.setText(lines[i]); // 追加文本
        }
    }

    /**
     * 替换表格内容，支持JSON格式数据
     *
     * @param document        Word文档对象
     * @param searchText      要搜索的文本
     * @param jsonReplacement JSON格式的表格数据
     */
    public static void replaceTableContent(XWPFDocument document, String searchText, String jsonReplacement) {
        try {
            // 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> tableData = objectMapper.readValue(jsonReplacement,
                new TypeReference<List<Map<String, Object>>>() {});

            // 在占位符位置创建新表格
            createNewTableWithData(document, searchText, tableData);

        } catch (Exception e) {
            log.error("解析表格JSON数据失败: {}", jsonReplacement, e);
            // 如果JSON解析失败，按普通文本处理
            Map<String, String> placeholder = new java.util.HashMap<>();
            placeholder.put(searchText.replace("${", "").replace("}", ""), jsonReplacement);
            replacePlaceholders(document, placeholder);
        }
    }

    /**
     * 在占位符位置创建新表格
     */
    private static void createNewTableWithData(XWPFDocument document, String searchText, List<Map<String, Object>> tableData) {
        if (tableData.isEmpty()) {
            return;
        }

        // 获取列名（从第一行数据中获取）
        Map<String, Object> firstRow = tableData.get(0);
        String[] columnNames = firstRow.keySet().toArray(new String[0]);

        // 查找包含占位符的段落
        XWPFParagraph targetParagraph = null;

        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String paragraphText = paragraph.getText();
            if (paragraphText != null && paragraphText.contains(searchText)) {
                targetParagraph = paragraph;
                break;
            }
        }

        // 如果找到了包含占位符的段落，在其位置插入表格
        if (targetParagraph != null) {
            // 清空占位符段落的内容
            while (!targetParagraph.getRuns().isEmpty()) {
                targetParagraph.removeRun(0);
            }

            // 在文档中创建表格（POI没有直接在指定位置插入表格的简单方法，所以在文档末尾创建）
            XWPFTable table = document.createTable();

            // 创建表头
            XWPFTableRow headerRow = table.getRow(0);
            // 确保表头有足够的单元格
            while (headerRow.getTableCells().size() < columnNames.length) {
                headerRow.addNewTableCell();
            }

            // 填充表头
            for (int j = 0; j < columnNames.length && j < headerRow.getTableCells().size(); j++) {
                XWPFTableCell cell = headerRow.getTableCells().get(j);
                cell.setText(columnNames[j]);

                // 设置表头样式
                XWPFParagraph paragraph = cell.getParagraphs().get(0);
                XWPFRun run = paragraph.createRun();
                run.setText(columnNames[j]);
                run.setBold(true);
                run.setFontSize(11);
            }

            // 填充数据行
            for (int i = 0; i < tableData.size(); i++) {
                Map<String, Object> rowData = tableData.get(i);
                XWPFTableRow row = table.createRow();

                // 确保行有足够的单元格
                while (row.getTableCells().size() < columnNames.length) {
                    row.addNewTableCell();
                }

                // 填充单元格数据
                for (int j = 0; j < columnNames.length && j < row.getTableCells().size(); j++) {
                    XWPFTableCell cell = row.getTableCells().get(j);
                    Object value = rowData.get(columnNames[j]);
                    cell.setText(value != null ? value.toString() : "");
                }
            }
        }
    }

    /**
     * 将文档写入输出流
     *
     * @param document       Word文档对象
     * @param outputStream 输出流
     * @throws IOException 如果写入失败
     */
    public static void writeDocument(XWPFDocument document, OutputStream outputStream) throws IOException {
        document.write(outputStream);
    }
}
