/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.GetObjectArgs;
import org.springblade.modules.yjzb.mapper.FinanceAnalysisReportMapper;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO;
import org.springblade.modules.yjzb.service.IFinanceAnalysisReportService;
import org.springblade.modules.yjzb.service.IFinanceAnalysisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

/**
 * 财务分析报告列表服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceAnalysisReportServiceImpl extends ServiceImpl<FinanceAnalysisReportMapper, FinanceAnalysisReportEntity>
        implements IFinanceAnalysisReportService {

    private final FinanceAnalysisReportMapper reportMapper;
    private final IFinanceAnalysisService financeAnalysisService;

    @Value("${oss.endpoint:http://127.0.0.1:9000}")
    private String minioEndpoint;

    @Value("${oss.access-key:}")
    private String minioAccessKey;

    @Value("${oss.secret-key:}")
    private String minioSecretKey;

    @Value("${oss.bucket-name:yjyc}")
    private String bucketName;

    @Override
    public IPage<FinanceAnalysisReportVO> selectReportPage(IPage<FinanceAnalysisReportVO> page, FinanceAnalysisReportVO report) {
        return reportMapper.selectReportPage(page, report);
    }

    @Override
    public Map<String, Object> getReportStatistics() {
        return reportMapper.getReportStatistics();
    }

    @Override
    public FinanceAnalysisReportVO getReportDetail(Long id) {
        return reportMapper.selectReportDetail(id);
    }

    @Override
    public FinanceAnalysisReportEntity createOrUpdateReport(String title, String type, Integer queryYear,
                                                            Integer compareYear, Integer startMonth, Integer endMonth) {
        // 查询是否已存在未删除的同名报告
        LambdaQueryWrapper<FinanceAnalysisReportEntity> wrapper =
                new LambdaQueryWrapper<>();
        wrapper.eq(FinanceAnalysisReportEntity::getTitle, title)
                .eq(FinanceAnalysisReportEntity::getIsDeleted, 0);
        FinanceAnalysisReportEntity existingReport = this.getOne(wrapper);

        boolean isUpdate = false;
        FinanceAnalysisReportEntity report;

        if (existingReport != null) {
            report = existingReport;
            isUpdate = true;
        } else {
            report = new FinanceAnalysisReportEntity();
            report.setTitle(title);
            report.setDownloadCount(0);
            report.setGenerateTime(new Date());
        }

        // 更新字段
        report.setType(type);
        report.setQueryYear(queryYear);
        report.setCompareYear(compareYear);
        report.setStartMonth(startMonth);
        report.setEndMonth(endMonth);
        report.setReportStatus("generating");
        report.setPeriod(String.format("%d年%d-%d月", queryYear, startMonth, endMonth));
        report.setUpdateTime(new Date());
        report.setGenerateTime(new Date()); //生成时间给当前时间

        if (isUpdate) {
            this.updateById(report);
        } else {
            this.save(report);
        }

        return report;
    }

    @Override
    public boolean updateReportStatus(Long reportId, String status, String errorMessage) {
        FinanceAnalysisReportEntity report = this.getById(reportId);
        if (report == null) {
            return false;
        }

        report.setReportStatus(status);
        if (Func.isNotBlank(errorMessage)) {
            report.setErrorMessage(errorMessage);
        }

        if ("completed".equals(status)) {
            report.setCompleteTime(new Date());
        }

        return this.updateById(report);
    }

    @Override
    public boolean completeReport(Long reportId, String filePath, String fileName, Long fileSize) {
        FinanceAnalysisReportEntity report = this.getById(reportId);
        if (report == null) {
            return false;
        }

        report.setReportStatus("completed");
        report.setFilePath(filePath);
        report.setFileName(fileName);
        report.setFileSize(fileSize);
        report.setCompleteTime(new Date());

        return this.updateById(report);
    }

    @Override
    public boolean incrementDownloadCount(Long id) {
        return reportMapper.updateDownloadCount(id) > 0;
    }

    @Override
    public boolean deleteReports(String ids) {
        return this.removeByIds(Func.toLongList(ids));
    }

    @Override
    public Long generateAnalysisReport(String title, String type, Integer queryYear,
                                       Integer compareYear, Integer startMonth, Integer endMonth) {
        // 1. 创建报告记录
        FinanceAnalysisReportEntity report = createOrUpdateReport(title, type, queryYear, compareYear, startMonth, endMonth);

        // 2. 异步执行分析任务
        CompletableFuture.runAsync(() -> {
            try {
                executeAnalysisAndGenerateReport(report);
            } catch (Exception e) {
                log.error("生成分析报告失败: reportId={}", report.getId(), e);
                updateReportStatus(report.getId(), "failed", e.getMessage());
            }
        });

        return report.getId();
    }

    /**
     * 执行分析并生成报告
     */
    private void executeAnalysisAndGenerateReport(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始执行分析报告生成: reportId={}, title={}", report.getId(), report.getTitle());

            // 执行各项分析
            // executeAnalysisTasks(report);

            // 生成Word文档
            generateWordDocument(report);

            log.info("分析报告生成完成: reportId={}", report.getId());

        } catch (Exception e) {
            log.error("执行分析报告生成失败: reportId={}", report.getId(), e);
            updateReportStatus(report.getId(), "failed", e.getMessage());
            throw e;
        }
    }

    /**
     * 执行分析任务
     */
    private void executeAnalysisTasks(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始执行分析任务: reportId={}, title={}", report.getId(), report.getTitle());

            // 执行四项分析任务
            List<FinanceAnalysisEntity> analysisResults = executeAllAnalysis(report);

            // 等待所有分析完成
            waitForAnalysisCompletion(analysisResults);

            log.info("分析任务执行完成: reportId={}", report.getId());

        } catch (Exception e) {
            log.error("执行分析任务失败: reportId={}", report.getId(), e);
            throw new RuntimeException("执行分析任务失败", e);
        }
    }

    /**
     * 执行所有分析
     */
    private List<FinanceAnalysisEntity> executeAllAnalysis(FinanceAnalysisReportEntity report) throws Exception {
        List<FinanceAnalysisEntity> results = new java.util.ArrayList<>();

        try {
            // 1. 实现税利情况分析+卷烟经营情况分析
            log.info("执行实现税利情况分析: reportId={}", report.getId());
            String workflowRunId1 = financeAnalysisService.analyzeMainEconomicIndicators(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId1 != null) {
                String[] workflowRunIds1 = workflowRunId1.split(",");
                for (String id : workflowRunIds1) {
                    FinanceAnalysisEntity entity1 = financeAnalysisService.getOneByWorkflowRunId(id);
                    if (entity1 != null) {
                        results.add(entity1);
                    }
                }

            }

            // 2. 三项费用支出总体情况分析
            log.info("执行三项费用支出总体情况分析: reportId={}", report.getId());
            String workflowRunId3 = financeAnalysisService.analyzeThreeExpenses(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId3 != null) {
                FinanceAnalysisEntity entity3 = financeAnalysisService.getOneByWorkflowRunId(workflowRunId3);
                if (entity3 != null) {
                    results.add(entity3);
                }
            }

            // 3. 重点费用支出情况分析
            log.info("执行重点费用支出情况分析: reportId={}", report.getId());
            String workflowRunId4 = financeAnalysisService.analyzeKeyExpenses(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId4 != null) {
                FinanceAnalysisEntity entity4 = financeAnalysisService.getOneByWorkflowRunId(workflowRunId4);
                if (entity4 != null) {
                    results.add(entity4);
                }
            }

        } catch (Exception e) {
            log.error("执行分析失败: reportId={}", report.getId(), e);
            throw e;
        }

        return results;
    }

    /**
     * 等待分析完成
     */
    private void waitForAnalysisCompletion(List<FinanceAnalysisEntity> analysisResults) {
        int maxWaitTime = 300; // 最大等待5分钟
        int waitInterval = 10; // 每10秒检查一次
        int waitedTime = 0;

        while (waitedTime < maxWaitTime) {
            boolean allCompleted = true;

            for (FinanceAnalysisEntity entity : analysisResults) {
                FinanceAnalysisEntity current = financeAnalysisService.getById(entity.getId());
                if (current == null || !"COMPLETED".equals(current.getExecuteStatus())) {
                    allCompleted = false;
                    break;
                }
            }

            if (allCompleted) {
                log.info("所有分析任务已完成");
                return;
            }

            try {
                Thread.sleep(waitInterval * 1000);
                waitedTime += waitInterval;
                log.info("等待分析完成中... 已等待{}秒", waitedTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待分析完成被中断", e);
            }
        }

        log.warn("等待分析完成超时，继续生成报告");
    }

    /**
     * 生成Word文档
     */
    private void generateWordDocument(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始生成Word文档: reportId={}", report.getId());

            // 获取分析结果
            List<FinanceAnalysisEntity> analysisResults = getAnalysisResults(report);

            // 生成Word文档内容
            byte[] documentBytes = createWordDocument(report, analysisResults);

            // 上传到MinIO
            String fileName = generateFileName(report);
            String filePath = uploadToMinio(documentBytes, fileName);

            // 更新报告状态
            completeReport(report.getId(), filePath, fileName, (long) documentBytes.length);

            log.info("Word文档生成完成: reportId={}, fileName={}, size={}",
                    report.getId(), fileName, documentBytes.length);

        } catch (Exception e) {
            log.error("生成Word文档失败: reportId={}", report.getId(), e);
            throw new RuntimeException("生成Word文档失败", e);
        }
    }

    /**
     * 获取分析结果
     */
    private List<FinanceAnalysisEntity> getAnalysisResults(FinanceAnalysisReportEntity report) {
        // 根据报告的查询条件获取对应的分析结果
        return financeAnalysisService.list(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<FinanceAnalysisEntity>()
                        .eq(FinanceAnalysisEntity::getQueryYear, report.getQueryYear())
                        .eq(FinanceAnalysisEntity::getCompareYear, report.getCompareYear())
                        .eq(FinanceAnalysisEntity::getStartMonth, report.getStartMonth())
                        .eq(FinanceAnalysisEntity::getEndMonth, report.getEndMonth())
                        .eq(FinanceAnalysisEntity::getExecuteStatus, "COMPLETED")
                        .orderByDesc(FinanceAnalysisEntity::getExecuteTime)
        );
    }

    /**
     * 创建Word文档
     */
    private byte[] createWordDocument(FinanceAnalysisReportEntity report, List<FinanceAnalysisEntity> analysisResults)
            throws IOException {
        try (XWPFDocument document = loadTemplate();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 替换模板中的内容
            replaceTemplateContent(document, analysisResults, report);

            document.write(out);
            return out.toByteArray();
        }
    }



    /**
     * 加载Word模板文件
     */
    private XWPFDocument loadTemplate() throws IOException {
        String templatePath = "templates/财务分析下载模板.docx";
        ClassPathResource resource = new ClassPathResource(templatePath);

        if (!resource.exists()) {
            log.error("模板文件不存在: classpath:{}", templatePath);
            throw new IOException("模板文件不存在: " + templatePath);
        }

        try (InputStream is = resource.getInputStream()) {
            return new XWPFDocument(is);
        }
    }

    /**
     * 替换模板中的内容
     */
    private void replaceTemplateContent(XWPFDocument document, List<FinanceAnalysisEntity> analysisResults, FinanceAnalysisReportEntity report) {
        replaceAllTextContent(document, "${年}", report.getQueryYear() + "");
        replaceAllTextContent(document, "${开始月}", report.getStartMonth() + "");
        replaceAllTextContent(document, "${结束月}", report.getEndMonth() + "");
        replaceAllTextContent(document, "{结束月最后一天}", getLastDayOfMonth(report.getQueryYear(), report.getStartMonth()) + "");
        replaceAllTextContent(document, "{生成日期}",  DateUtil.format(report.getGenerateTime(), "yyyy年MM月dd日"));

        for (FinanceAnalysisEntity analysis : analysisResults) {
            if (analysis.getName() == null || analysis.getAnswerContent() == null) {
                continue;
            }

            String name = analysis.getName();
            String content = analysis.getAnswerContent();
            String tableDataList = analysis.getResult();

            switch (name) {
                case "主要经济指标表格":
                    replaceTableContent(document, "${economic_indicators}", tableDataList);
                    break;
                case "实现税利情况分析":
                    replaceTextContent(document, "${tax_profit_analysis}", content);
                    break;
                case "卷烟经营情况分析":
                    replaceTextContent(document, "${cigarette_operation_analysis}", content);
                    break;
                case "三项费用支出总体情况分析":
                    replaceTextContent(document, "${three_expenses_analysis}", content);
                    break;
                case "三项费用表格":
                    replaceTableContent(document, "${three_expenses}", tableDataList);
                    break;
                case "重点费用支出情况分析":
                    replaceTextContent(document, "${key_expenses_analysis}", content);
                    break;
                case "重点费用支出情况表格":
                    replaceTableContent(document, "${key_expenses}", tableDataList);
                    break;
                default:
                    log.warn("未知的分析类型: {}", name);
                    break;
            }
        }
    }

    /**
     * 替换文本内容
     */
    private void replaceTextContent(XWPFDocument document, String searchText, String replacement) {
        // 替换段落中的文本
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, searchText, replacement);
        }
    }

    /**
     * 全文档替换文本内容（包括表格）
     */
    private void replaceAllTextContent(XWPFDocument document, String searchText, String replacement) {
        // 替换段落中的文本
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, searchText, replacement);
        }

        // 替换表格中的文本
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceInParagraph(paragraph, searchText, replacement);
                    }
                }
            }
        }

        // 替换页眉中的文本
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                replaceInParagraph(paragraph, searchText, replacement);
            }
        }

        // 替换页脚中的文本
        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                replaceInParagraph(paragraph, searchText, replacement);
            }
        }
    }

    /**
     * 在段落中替换文本+ 格式继承，保留换行
     */
    private void replaceInParagraph(XWPFParagraph paragraph, String searchText, String replacement) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) {
            return;
        }

        for (XWPFRun run : runs) {
            String text = run.getText(0); // 获取 Run 的原始文本
            if (text == null || !text.contains(searchText)) {
                continue;
            }

            // 1. 记录原 Run 的格式
            Integer fontSize = run.getFontSize();           // 字号
            String fontFamily = run.getFontFamily();       // 字体
            boolean isBold = run.isBold();                 // 加粗
            boolean isItalic = run.isItalic();             // 斜体
            String color = run.getColor();                 // 颜色（十六进制，如 "FF0000"）

            // 2. 清空原run内容
            run.setText("", 0);

            // 3. 执行替换并处理换行
            String newText = text.replace(searchText, replacement);
            String[] lines = newText.split("\\r?\\n");

            for (int i = 0; i < lines.length; i++) {
                if (i > 0) {
                    run.addBreak(); // 添加换行符
                }
                run.setText(lines[i]);
            }

            // 4. 将原格式应用到新文本上
            if (fontSize != null) {
                run.setFontSize(fontSize);
            }
            if (fontFamily != null) {
                run.setFontFamily(fontFamily);
            }
            run.setBold(isBold);
            run.setItalic(isItalic);
            if (color != null) {
                run.setColor(color);
            }

            // 注意：一旦替换完成，就退出（避免重复替换）
            break;
        }
    }

    /**
     * 替换表格内容，支持JSON格式数据
     */
    private void replaceTableContent(XWPFDocument document, String searchText, String jsonReplacement) {
        try {
            // 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> tableData = objectMapper.readValue(jsonReplacement,
                new TypeReference<List<List<String>>>() {});

            // 查找包含搜索文本的表格
            for (XWPFTable table : document.getTables()) {
                boolean found = false;
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            String cellText = paragraph.getText();
                            if (cellText != null && cellText.contains(searchText)) {
                                found = true;
                                break;
                            }
                        }
                        if (found) break;
                    }
                    if (found) break;
                }

                if (found) {
                    // 替换表格数据
                    replaceTableWithData(table, tableData);
                    return;
                }
            }

            // 如果没有找到现有表格，创建新表格
            createNewTableWithData(document, tableData);

        } catch (Exception e) {
            log.error("解析表格JSON数据失败: {}", jsonReplacement, e);
            // 如果JSON解析失败，按普通文本处理
            replaceAllTextContent(document, searchText, jsonReplacement);
        }
    }

    /**
     * 用新数据替换现有表格
     */
    private void replaceTableWithData(XWPFTable table, List<List<String>> tableData) {
        // 清空现有表格内容
        while (table.getRows().size() > 0) {
            table.removeRow(0);
        }

        // 填充新数据
        for (int i = 0; i < tableData.size(); i++) {
            List<String> rowData = tableData.get(i);
            XWPFTableRow row = table.createRow();

            // 确保行有足够的单元格
            while (row.getTableCells().size() < rowData.size()) {
                row.addNewTableCell();
            }

            // 填充单元格数据
            for (int j = 0; j < rowData.size() && j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getTableCells().get(j);
                cell.setText(rowData.get(j));

                // 设置单元格样式
                if (i == 0) { // 表头
                    XWPFParagraph paragraph = cell.getParagraphs().get(0);
                    XWPFRun run = paragraph.getRuns().get(0);
                    if (run != null) {
                        run.setBold(true);
                        run.setFontSize(11);
                    }
                }
            }
        }
    }

    /**
     * 创建新表格
     */
    private void createNewTableWithData(XWPFDocument document, List<List<String>> tableData) {
        if (tableData.isEmpty()) {
            return;
        }

        XWPFTable table = document.createTable();

        for (int i = 0; i < tableData.size(); i++) {
            List<String> rowData = tableData.get(i);
            XWPFTableRow row = (i == 0) ? table.getRow(0) : table.createRow();

            // 确保行有足够的单元格
            while (row.getTableCells().size() < rowData.size()) {
                row.addNewTableCell();
            }

            // 填充单元格数据
            for (int j = 0; j < rowData.size() && j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getTableCells().get(j);
                cell.setText(rowData.get(j));

                // 设置单元格样式
                if (i == 0) { // 表头
                    XWPFParagraph paragraph = cell.getParagraphs().get(0);
                    XWPFRun run = paragraph.createRun();
                    run.setText(rowData.get(j));
                    run.setBold(true);
                    run.setFontSize(11);
                }
            }
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(FinanceAnalysisReportEntity report) {
        return String.format("%d年%d-%d月财务分析.docx",
                report.getQueryYear(), report.getStartMonth(), report.getEndMonth());
    }

    /**
     * 上传到MinIO
     */
    private String uploadToMinio(byte[] documentBytes, String fileName) {
        try {
            // 直接使用MinioClient上传文件，避免依赖HTTP请求上下文
            String objectName = "finance/reports/" + System.currentTimeMillis() + "_" + fileName;

            MinioClient minioClient = getMinioClient();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(documentBytes);

            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, documentBytes.length, -1)
                            .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                            .build()
            );

            log.info("文件上传到MinIO成功: objectName={}, size={}", objectName, documentBytes.length);
            return objectName;

        } catch (Exception e) {
            log.error("上传文件到MinIO失败: fileName={}", fileName, e);
            throw new RuntimeException("上传文件失败", e);
        }
    }

    /**
     * 获取MinioClient实例
     */
    private MinioClient getMinioClient() {
        try {
            return MinioClient.builder()
                    .endpoint(minioEndpoint)
                    .credentials(minioAccessKey, minioSecretKey)
                    .build();
        } catch (Exception e) {
            log.error("创建MinioClient失败", e);
            throw new RuntimeException("无法创建MinioClient", e);
        }
    }

    @Override
    public byte[] downloadReportData(Long id) {
        try {
            FinanceAnalysisReportEntity report = getById(id);
            if (report == null) {
                log.error("报告不存在，ID: {}", id);
                return null;
            }

            if (Func.isBlank(report.getFilePath())) {
                log.error("报告文件路径为空，ID: {}", id);
                return null;
            }

            if (!"completed".equals(report.getReportStatus())) {
                log.error("报告尚未生成完成，ID: {}", id);
                return null;
            }

            // 从MinIO下载文件
            return downloadFromMinio(report.getFilePath());

        } catch (Exception e) {
            log.error("下载报告数据失败: reportId={}", id, e);
            return null;
        }
    }

    /**
     * 从MinIO下载文件
     */
    private byte[] downloadFromMinio(String objectName) {
        try {
            MinioClient minioClient = getMinioClient();
            try (var inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build())) {

                return inputStream.readAllBytes();
            }
        } catch (Exception e) {
            log.error("从MinIO下载文件失败: objectName={}", objectName, e);
            throw new RuntimeException("下载文件失败", e);
        }
    }

    /**
     * 获取指定年月的最后一天
     */
    private int getLastDayOfMonth(int year, int month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            case 2:
                // 判断闰年
                if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                    return 29;
                } else {
                    return 28;
                }
            default:
                return 31;
        }
    }
}
