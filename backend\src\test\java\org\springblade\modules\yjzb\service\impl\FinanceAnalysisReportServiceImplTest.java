package org.springblade.modules.yjzb.service.impl;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FinanceAnalysisReportServiceImpl 测试类
 */
public class FinanceAnalysisReportServiceImplTest {

    @InjectMocks
    private FinanceAnalysisReportServiceImpl financeAnalysisReportService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testReplaceInParagraphWithLineBreaks() throws Exception {
        // 创建测试文档
        XWPFDocument document = new XWPFDocument();
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("这是测试文本${placeholder}结束");

        // 使用反射调用私有方法
        Method method = FinanceAnalysisReportServiceImpl.class.getDeclaredMethod(
                "replaceInParagraphWithLineBreaks", XWPFParagraph.class, String.class, String.class);
        method.setAccessible(true);

        // 测试包含换行的替换
        String replacement = "第一行\n第二行\n第三行";
        method.invoke(financeAnalysisReportService, paragraph, "${placeholder}", replacement);

        // 验证结果
        String resultText = paragraph.getText();
        assertTrue(resultText.contains("第一行"));
        assertTrue(resultText.contains("第二行"));
        assertTrue(resultText.contains("第三行"));

        document.close();
    }

    @Test
    void testWordTemplateUtilIntegration() throws Exception {
        // 创建测试文档
        XWPFDocument document = new XWPFDocument();

        // 添加段落
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("测试${年}年度报告");

        // 添加表格
        document.createTable(2, 2);
        document.getTables().get(0).getRow(0).getCell(0).setText("${年}年数据");

        // 准备占位符数据
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("年", "2025");

        // 使用WordTemplateUtil进行替换
        org.springblade.modules.yjzb.util.WordTemplateUtil.replacePlaceholders(document, placeholders);

        // 验证段落替换
        String paragraphText = document.getParagraphs().get(0).getText();
        assertTrue(paragraphText.contains("2025年度报告"));

        // 验证表格替换
        String cellText = document.getTables().get(0).getRow(0).getCell(0).getText();
        assertTrue(cellText.contains("2025年数据"));

        document.close();
    }

    @Test
    void testReplaceTableContentWithJson() throws Exception {
        // 创建测试文档
        XWPFDocument document = new XWPFDocument();
        
        // 添加包含搜索文本的段落
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("表1. 主要经济指标");

        // 使用反射调用私有方法
        Method method = FinanceAnalysisReportServiceImpl.class.getDeclaredMethod(
                "replaceTableContent", XWPFDocument.class, String.class, String.class);
        method.setAccessible(true);

        // 测试JSON数据
        String jsonData = "[[\"指标名称\", \"本期数据\", \"同期数据\"], [\"营业收入\", \"1000万\", \"900万\"], [\"利润总额\", \"200万\", \"180万\"]]";

        // 执行替换
        method.invoke(financeAnalysisReportService, document, "表1. 主要经济指标", jsonData);

        // 验证表格是否创建
        assertFalse(document.getTables().isEmpty());
        
        // 验证表格内容
        if (!document.getTables().isEmpty()) {
            assertEquals("指标名称", document.getTables().get(0).getRow(0).getCell(0).getText());
            assertEquals("营业收入", document.getTables().get(0).getRow(1).getCell(0).getText());
        }

        document.close();
    }

    @Test
    void testCreateFinanceAnalysisEntities() {
        // 创建测试数据
        FinanceAnalysisEntity entity1 = new FinanceAnalysisEntity();
        entity1.setName("主要经济指标");
        entity1.setAnswerContent("[[\"指标\", \"数值\"], [\"收入\", \"1000\"]]");

        FinanceAnalysisEntity entity2 = new FinanceAnalysisEntity();
        entity2.setName("实现税利情况分析");
        entity2.setAnswerContent("税利情况良好\n同比增长10%\n环比增长5%");

        List<FinanceAnalysisEntity> entities = Arrays.asList(entity1, entity2);

        // 验证数据
        assertEquals(2, entities.size());
        assertEquals("主要经济指标", entities.get(0).getName());
        assertTrue(entities.get(1).getAnswerContent().contains("税利情况良好"));
    }

    @Test
    void testCreateReportEntity() {
        // 创建测试报告实体
        FinanceAnalysisReportEntity report = new FinanceAnalysisReportEntity();
        report.setQueryYear(2025);
        report.setStartMonth(1);
        report.setEndMonth(5);
        report.setTitle("2025年1-5月财务分析报告");

        // 验证数据
        assertEquals(Integer.valueOf(2025), report.getQueryYear());
        assertEquals(Integer.valueOf(1), report.getStartMonth());
        assertEquals(Integer.valueOf(5), report.getEndMonth());
        assertEquals("2025年1-5月财务分析报告", report.getTitle());
    }
}
